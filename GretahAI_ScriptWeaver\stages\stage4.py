"""
Stage 4: UI Element Detection and Test Case Step Selection

This module handles UI element detection, interactive element selection,
and element matching for test case steps.
Maintains the StateManager pattern and follows the established architectural patterns.
"""

import os
import logging
import streamlit as st
import time
from datetime import datetime

# Configure logging
logger = logging.getLogger("ScriptWeaver.stage4")

# Import helper functions from other modules
from core.element_detection import detect_elements, detect_elements_advanced, filter_qa_relevant_elements, select_element_interactively
from core.element_matching import match_elements_with_ai
from helpers_pure import analyze_step_for_test_data

def stage4_ui_detection_and_matching(state):
    """Phase 4: UI Element Detection and Test Case Step Selection."""
    st.markdown("<h2 class='stage-header'>Phase 4: UI Element Detection</h2>", unsafe_allow_html=True)

    # Check if we have a stage progression message to display
    if 'stage_progression_message' in st.session_state:
        st.success(st.session_state['stage_progression_message'])
        # Remove the message so it doesn't show up again
        del st.session_state['stage_progression_message']

    # Check if we're coming from Stage 7 (automatic advancement)
    if 'coming_from_stage7' in st.session_state:
        # Get information about the advancement
        if 'force_refresh_after_advance' in st.session_state:
            from_step = st.session_state['force_refresh_after_advance'].get('from_step')
            target_step = st.session_state['force_refresh_after_advance'].get('target_step')

            # Display a compact banner
            st.success(f"✅ Advanced from Step {from_step} to Step {target_step}")

        # Clear the flag so it doesn't show up again
        del st.session_state['coming_from_stage7']

    # Check if we need to force a refresh after automatic advancement
    if 'force_refresh_after_advance' in st.session_state:
        # Get the timestamp to see if this is a recent advancement
        advance_time = st.session_state['force_refresh_after_advance'].get('timestamp')
        from datetime import datetime, timedelta
        now = datetime.now()

        # If the advancement was within the last 5 seconds, force a refresh
        if advance_time and (now - datetime.strptime(advance_time, "%H:%M:%S.%f")) < timedelta(seconds=5):
            # Clear the flag so we don't keep refreshing
            target_step = st.session_state['force_refresh_after_advance'].get('target_step')
            logger.info(f"Forcing refresh to ensure UI shows step {target_step}")
            del st.session_state['force_refresh_after_advance']

            # Force a rerun to refresh the UI
            time.sleep(0.5)
            st.rerun()

    # Check if prerequisites are met
    if not hasattr(state, 'step_table_json') or not state.step_table_json:
        st.warning("⚠️ Please complete Phase 3 first to convert the test case")
        return

    # Step selection section in a collapsible section
    with st.expander("Step Selection", expanded=True):
        # Add a reset button to start over with step 1
        reset_cols = st.columns([3, 1])
        with reset_cols[0]:
            if st.button("Reset to Step 1", key="reset_step_btn", help="Reset to the first step of the test case"):
                # Check if we have progress to confirm reset
                has_progress = (hasattr(state, 'current_step_index') and state.current_step_index > 0) or state.all_steps_done

                if has_progress:
                    st.warning("⚠️ Resetting will clear all progress.")
                    confirm_reset = st.button("Confirm Reset", key="confirm_reset_step")

                    if not confirm_reset:
                        st.info("Reset cancelled.")
                        return

                # Use the state manager's update method to reset step progress
                state.update_step_progress(
                    current_step_index=0,
                    all_steps_done=False,
                    step_ready_for_script=False,
                    script_just_generated=False
                )

                # Reset step-specific state
                state.reset_step_state(confirm=True, reason="User requested reset to Step 1")

                st.success("✅ Reset complete.")
                st.rerun()

    # Get total steps if not already set
    if state.total_steps == 0 and state.step_table_json:
        state.total_steps = len(state.step_table_json)
        logger.info(f"State change: total_steps = {state.total_steps}")

    # Check if all steps are done
    if state.all_steps_done:
        st.success("✅ All test case steps have been processed!")

        # Show option to start over or go to Stage 3
        col1, col2 = st.columns(2)
        with col1:
            if st.button("Start Over", key="start_over_btn", help="Reset and start from Step 1"):
                # Reset all step progress
                state.update_step_progress(
                    current_step_index=0,
                    all_steps_done=False,
                    step_ready_for_script=False,
                    script_just_generated=False
                )
                state.reset_step_state(confirm=True, reason="User chose to start over")
                st.rerun()

        with col2:
            if st.button("Select New Test Case", key="new_test_case_btn", help="Go to Stage 3 to select a different test case"):
                # Reset test case state
                state.reset_test_case_state(confirm=True, reason="User chose to select a new test case")
                st.rerun()

        return

    # Get the current step from the step table
    current_step_index = state.current_step_index
    if current_step_index < len(state.step_table_json):
        selected_step_table_entry = state.step_table_json[current_step_index]
        state.selected_step_table_entry = selected_step_table_entry

        # Find the corresponding original step
        if state.selected_test_case and 'Steps' in state.selected_test_case:
            original_steps = state.selected_test_case.get('Steps', [])
            step_no = str(selected_step_table_entry.get('step_no', ''))

            try:
                selected_original_step = next(
                    (step for step in original_steps if str(step.get('Step No')) == step_no),
                    None
                )
            except Exception as e:
                logger.error(f"Error finding original step: {e}")
                selected_original_step = None

            if selected_original_step:
                state.selected_step = selected_original_step

                # Display current step information in a collapsible section
                with st.expander("Current Step Information", expanded=True):
                    step_tab1, step_tab2 = st.tabs(["Automation Format", "Original Format"])

                    with step_tab1:
                        # Display the step table entry
                        st.markdown(f"**Step:** {selected_step_table_entry.get('step_no')}")
                        st.markdown(f"**Action:** {selected_step_table_entry.get('action')}")
                        st.markdown(f"**Locator Strategy:** {selected_step_table_entry.get('locator_strategy')}")
                        if selected_step_table_entry.get('locator'):
                            st.markdown(f"**Locator:** {selected_step_table_entry.get('locator')}")

                    with step_tab2:
                        # Display the original step details
                        st.markdown(f"**Step Number:** {selected_original_step.get('Step No')}")
                        st.markdown(f"**Test Steps:** {selected_original_step.get('Test Steps')}")
                        st.markdown(f"**Expected Result:** {selected_original_step.get('Expected Result')}")

                # Stage 4b: UI Element Detection
                _handle_ui_element_detection(state, selected_step_table_entry, selected_original_step)

                # Stage 4c: Element Matching
                _handle_element_matching(state, selected_step_table_entry, selected_original_step)

def _handle_ui_element_detection(state, selected_step_table_entry, selected_original_step):
    """Handle UI element detection for the selected test case step."""
    with st.expander("UI Element Detection", expanded=True):
        # Check if we have a step table analysis
        requires_ui_elements = True
        ui_element_reason = "UI element detection is needed for proper automation."

        if hasattr(state, 'step_table_analysis') and state.step_table_analysis:
            step_table_analysis = state.step_table_analysis
            requires_ui_elements = step_table_analysis.get("requires_ui_elements", True)
            ui_element_reason = step_table_analysis.get("reason", ui_element_reason)

        # Check if this specific step requires UI elements
        step_requires_ui_elements = selected_step_table_entry.get('locator_strategy') not in ["", "none", "n/a", "url"]

        # Display message about UI element detection requirement
        if requires_ui_elements and step_requires_ui_elements:
            st.info(f"🔍 {ui_element_reason}")

            # Show the Detect UI Elements and Interactive Selection buttons
            detect_col1, detect_col2 = st.columns(2)
            with detect_col1:
                detect_button = st.button(
                    "🔍 Detect Elements Automatically",
                    key="detect_ui_elements_btn",
                    help="Automatically detect UI elements from the website for this step",
                    use_container_width=True
                )

            with detect_col2:
                select_interactive_button = st.button(
                    "👆 Select Element Interactively",
                    key="select_element_interactive_btn",
                    help="Open a browser window to manually select UI elements for this step",
                    use_container_width=True
                )

            if select_interactive_button:
                _handle_interactive_element_selection(state, selected_step_table_entry, selected_original_step)

            if detect_button:
                _handle_automatic_element_detection(state, selected_step_table_entry)
        else:
            # If UI element detection is not needed, show a message and set empty elements
            st.success(f"✓ Element detection not needed: {ui_element_reason}")

            # Create empty elements for the workflow to continue
            if not hasattr(state, 'detected_elements') or not state.detected_elements:
                state.detected_elements = []
                state.qa_relevant_elements = []
                state.step_elements = []

        # Display detected elements if available
        if hasattr(state, 'qa_relevant_elements') and state.qa_relevant_elements:
            st.markdown("**QA-Relevant Elements Detected:**")
            st.info(f"Found {len(state.qa_relevant_elements)} QA-relevant elements")
        elif hasattr(state, 'detected_elements') and state.detected_elements:
            st.markdown("**Elements Detected:**")
            st.info(f"Found {len(state.detected_elements)} elements")

def _handle_interactive_element_selection(state, selected_step_table_entry, selected_original_step):
    """Handle interactive element selection for the selected test case step."""
    with st.spinner("Opening browser for interactive element selection..."):
        try:
            # Get locator strategy and value from the step table entry
            locator_strategy = None
            locator_value = None

            if selected_step_table_entry:
                locator_strategy = selected_step_table_entry.get('locator_strategy')
                locator_value = selected_step_table_entry.get('locator')

            # Use the website URL from state
            website_url = state.website_url if hasattr(state, 'website_url') and state.website_url else "https://example.com"
            logger.info(f"Opening interactive element selector for: {website_url}")

            # Call the interactive element selection function
            selected_element = select_element_interactively(website_url)

            if selected_element:
                # Create a properly formatted element structure
                element = {
                    'name': selected_element.get('text', selected_element.get('tag', 'Unknown Element')),
                    'selector': selected_element.get('selector', ''),
                    'tag': selected_element.get('tag', ''),
                    'attributes': {
                        'id': selected_element.get('id', ''),
                        'name': selected_element.get('name', ''),
                        'class': selected_element.get('class', ''),
                        'type': selected_element.get('type', ''),
                        'role': selected_element.get('role', ''),
                        'aria-label': selected_element.get('aria-label', ''),
                        'placeholder': selected_element.get('placeholder', ''),
                        'value': selected_element.get('value', ''),
                        'href': selected_element.get('href', ''),
                        'src': selected_element.get('src', ''),
                        'alt': selected_element.get('alt', ''),
                        'title': selected_element.get('title', '')
                    },
                    'text': selected_element.get('text', ''),
                    'position': {
                        'x': selected_element.get('x', 0),
                        'y': selected_element.get('y', 0),
                        'width': selected_element.get('width', 0),
                        'height': selected_element.get('height', 0)
                    },
                    'interactive': True,
                    'visible': True,
                    'manually_selected': True,
                    'score': 100  # Give manually selected elements the highest score
                }

                # Store the element in state manager
                if not hasattr(state, 'detected_elements'):
                    state.detected_elements = []

                # Add the manually selected element to the beginning of the list
                state.detected_elements.insert(0, element)

                # Apply QA-specific filtering (though we'll keep the manually selected element)
                qa_elements = [element]  # Start with the manually selected element

                # Add any other elements that match the filtering criteria
                if hasattr(state, 'detected_elements') and len(state.detected_elements) > 1:
                    other_qa_elements = filter_qa_relevant_elements(
                        state.detected_elements[1:],  # Skip the first element which is our manually selected one
                        locator_strategy=locator_strategy,
                        locator_value=locator_value
                    )
                    qa_elements.extend(other_qa_elements)

                state.qa_relevant_elements = qa_elements

                # Show success message with element information
                st.success(f"✓ Element selected: {element['name']} ({element['selector']})")

                # Show the selected element details without using an expander (to avoid nesting)
                st.markdown("**Selected Element Details:**")
                st.json(element)

                # Automatically create element matches for the manually selected element
                # This allows skipping the element matching step (4c)
                _create_element_match_for_manual_selection(state, element, selected_step_table_entry, selected_original_step)
            else:
                st.warning("No element was selected or the selection timed out.")
        except Exception as e:
            st.error(f"Error during interactive element selection: {e}")
            import traceback
            st.error(traceback.format_exc())

def _create_element_match_for_manual_selection(state, element, selected_step_table_entry, selected_original_step):
    """Create element match for manually selected element for the selected test case step."""
    try:
        # Prepare the test case and step for automatic matching
        test_case_id = state.selected_test_case.get('Test Case ID')
        step_no = str(selected_original_step.get('Step No'))

        # Create a match entry for the manually selected element
        element_match = {
            'element': element,
            'action': selected_step_table_entry.get('action', 'interact with'),
            'score': 1.0,  # Perfect match score
            'reasoning': 'This element was manually selected by the user.',
            'manually_selected': True
        }

        # Create the element matches structure
        if not hasattr(state, 'element_matches'):
            state.element_matches = {}

        if test_case_id not in state.element_matches:
            state.element_matches[test_case_id] = {}

        state.element_matches[test_case_id][step_no] = [element_match]
        state.step_matches = state.element_matches

        # Set the LLM step analysis
        # Analyze if the step requires test data
        test_data_analysis = analyze_step_for_test_data(
            selected_step_table_entry,
            selected_original_step.get('Test Steps')
        )

        # Store the complete analysis in session state
        state.llm_step_analysis = {
            "requires_ui_element": True,
            "reason": "Manually selected UI element will be used for this step.",
            "matches": [element_match],
            "requires_test_data": test_data_analysis["requires_test_data"],
            "test_data_reason": test_data_analysis["reason"],
            "data_types": test_data_analysis["data_types"]
        }

        # Store test data analysis separately for easier access
        state.test_data_analysis = test_data_analysis

        # Automatically set test_data_skipped flag for navigation steps
        if not test_data_analysis["requires_test_data"]:
            state.test_data_skipped = True
            state.test_data = getattr(state, 'test_data', {})  # Ensure test_data exists
            st.success("✓ Test data configuration automatically skipped for this navigation test case step.")
            st.info("You can proceed directly to Application Stage 6 to generate the test script.")

        # Inform the user that element matching is complete
        if not test_data_analysis["requires_test_data"]:
            st.info("✓ Element matching automatically completed with your manually selected element. You can proceed directly to Application Stage 6.")
        else:
            st.info("✓ Element matching automatically completed with your manually selected element. You can proceed to Application Stage 5.")
    except Exception as e:
        st.warning(f"Could not automatically complete element matching: {e}. Please use the 'Match Elements with Step' button in Application Stage 4c.")

def _handle_automatic_element_detection(state, selected_step_table_entry):
    """Handle automatic element detection for the selected test case step."""
    with st.spinner("Detecting and filtering UI elements from the website..."):
        try:
            # Get locator strategy and value from the step table entry
            locator_strategy = None
            locator_value = None

            if selected_step_table_entry:
                locator_strategy = selected_step_table_entry.get('locator_strategy')
                locator_value = selected_step_table_entry.get('locator')

                # Display the locator strategy being used
                if locator_strategy and locator_strategy.lower() not in ['none', 'n/a', '']:
                    st.info(f"Using locator strategy: {locator_strategy}" +
                           (f" with value: {locator_value}" if locator_value else ""))

            # For step 1, extract from URL; for step 2+, extract from browser
            website_url = state.website_url if hasattr(state, 'website_url') and state.website_url else "https://example.com"
            logger.info(f"Using website URL for element detection: {website_url}")

            if hasattr(state, 'test_browser') and state.test_browser:
                # Use the live browser instance for extraction
                browser = state.test_browser
                elements = detect_elements(browser)  # This function should handle browser instance
            else:
                # First time: extract from URL with locator strategy
                elements = detect_elements_advanced(
                    website_url,
                    locator_strategy=locator_strategy,
                    locator_value=locator_value
                )
            state.detected_elements = elements

            # Apply QA-specific filtering to the detected elements using locator strategy
            qa_elements = filter_qa_relevant_elements(
                elements,
                locator_strategy=locator_strategy,
                locator_value=locator_value
            )
            state.qa_relevant_elements = qa_elements

            # Ensure step_elements is set for test data generation
            state.step_elements = qa_elements if qa_elements else elements

            # Show success message with locator strategy information
            if locator_strategy and locator_strategy.lower() not in ['none', 'n/a', '']:
                st.success(f"✓ Detected {len(elements)} UI elements and filtered to {len(qa_elements)} QA-relevant elements using {locator_strategy} strategy.")
            else:
                st.success(f"✓ Detected {len(elements)} UI elements and filtered to {len(qa_elements)} QA-relevant elements.")

            # Show filtered QA-relevant UI elements
            st.markdown("**QA-Relevant UI Elements:**")
            if qa_elements:
                st.write(f"{len(qa_elements)} QA-relevant UI elements detected:")
                st.json(qa_elements)
            else:
                st.info("No QA-relevant UI elements detected.")
        except Exception as e:
            st.error(f"Error detecting elements: {e}")
